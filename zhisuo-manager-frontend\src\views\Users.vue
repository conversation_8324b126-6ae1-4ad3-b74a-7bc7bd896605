<template>
  <div class="users-page">
    <!-- 统一的管理面板 -->
    <div class="management-panel">
      <!-- 页面标题和统计 -->
      <div class="panel-header">
        <div class="header-left">
          <h2>用户管理</h2>
        </div>
        <div class="header-right">
          <div class="header-stats">
            <div class="stat-item">
              <span class="stat-label">总用户数</span>
              <span class="stat-value">{{ totalUsers }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">今日新增</span>
              <span class="stat-value">{{ todayNewUsers }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">活跃用户</span>
              <span class="stat-value">{{ activeUsers }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="panel-divider"></div>

      <!-- 搜索和筛选区域 -->
      <div class="panel-filters">
        <div class="filters-content">
          <div class="search-section">
            <el-form :model="queryForm" inline class="search-form" label-width="80px">
              <el-form-item label="手机号">
                <el-input
                  v-model="queryForm.phone"
                  placeholder="请输入手机号"
                  clearable
                  style="width: 160px"
                />
              </el-form-item>

              <el-form-item label="昵称">
                <el-input
                  v-model="queryForm.nickname"
                  placeholder="请输入昵称"
                  clearable
                  style="width: 160px"
                />
              </el-form-item>

              <el-form-item label="会员等级">
                <el-select v-model="queryForm.memberLevel" placeholder="请选择" clearable style="width: 150px">
                  <el-option label="普通会员" :value="0" />
                  <el-option label="黄金会员" :value="1" />
                  <el-option label="铂金会员" :value="2" />
                </el-select>
              </el-form-item>

              <el-form-item label="状态">
                <el-select v-model="queryForm.status" placeholder="请选择" clearable style="width: 120px">
                  <el-option label="正常" :value="1" />
                  <el-option label="禁用" :value="0" />
                </el-select>
              </el-form-item>

              <el-form-item label="注册时间">
                <el-date-picker
                  v-model="queryForm.registerTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  clearable
                  style="width: 220px"
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="handleAddUser">
                  <el-icon><Plus /></el-icon>
                  添加用户
                </el-button>
                <el-button @click="handleExport">
                  <el-icon><Download /></el-icon>
                  导出数据
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <div class="action-section">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 用户列表 -->
    <div ref="userListRef" class="content-card">
      <div class="table-header">
        <div class="table-title">用户列表</div>
        <div class="table-actions">
          <el-button size="small" @click="handleRefresh" class="refresh-btn">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>
      
      <el-table
        v-loading="loading"
        :data="userList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="userId" label="用户ID" width="120" show-overflow-tooltip />
        
        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="nickname" label="昵称" width="120" show-overflow-tooltip />
        
        <el-table-column label="会员等级" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.memberLevel === 0 ? 'info' : row.memberLevel === 1 ? 'warning' : 'success'"
              size="small"
            >
              {{ row.memberLevelName }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
              {{ row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="favoriteCount" label="收藏数" width="80" />
        <el-table-column prop="likeCount" label="点赞数" width="80" />
        <el-table-column prop="commentCount" label="评论数" width="80" />
        
        <el-table-column prop="createTime" label="注册时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="lastLoginTime" label="最后登录" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastLoginTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button
              :type="row.status === 1 ? 'warning' : 'success'"
              size="small"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      width="650px"
      :before-close="handleCloseDetail"
      class="user-detail-dialog"
      title="用户详情"
    >
      <div v-if="currentUser" class="user-detail-content">
        <!-- 用户头像和基本信息 -->
        <div class="user-header">
          <div class="user-avatar">
            <el-avatar
              :size="60"
              :src="currentUser.avatar"
              :alt="currentUser.nickname || '用户' + currentUser.userId"
            >
              <el-icon><User /></el-icon>
            </el-avatar>
          </div>
          <div class="user-info">
            <h3 class="user-name">{{ currentUser.nickname || '用户' + currentUser.userId }}</h3>
            <div class="user-tags">
              <el-tag
                :type="currentUser.memberLevel === 0 ? 'info' : currentUser.memberLevel === 1 ? 'warning' : 'success'"
                size="small"
              >
                {{ currentUser.memberLevelName }}
              </el-tag>
              <el-tag
                :type="currentUser.status === 1 ? 'success' : 'danger'"
                size="small"
                class="status-tag"
              >
                {{ currentUser.statusName }}
              </el-tag>
            </div>
          </div>
          <!-- 活动统计 -->
          <div class="user-stats">
            <div class="stat-item">
              <div class="stat-number">{{ currentUser.favoriteCount }}</div>
              <div class="stat-label">收藏</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ currentUser.likeCount }}</div>
              <div class="stat-label">点赞</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ currentUser.commentCount }}</div>
              <div class="stat-label">评论</div>
            </div>
          </div>
        </div>

        <!-- 详细信息 -->
        <div class="detail-section">
          <div class="detail-group">
            <h4 class="group-title">基本信息</h4>
            <div class="detail-items">
              <div class="detail-item">
                <span class="item-label">用户ID</span>
                <div class="item-value-with-copy">
                  <el-tooltip :content="currentUser.userId" placement="top" class="tooltip-wrapper">
                    <span class="item-value truncated">{{ currentUser.userId }}</span>
                  </el-tooltip>
                  <el-button
                    type="text"
                    size="small"
                    class="copy-btn"
                    @click="copyToClipboard(currentUser.userId)"
                  >
                    <el-icon><CopyDocument /></el-icon>
                  </el-button>
                </div>
              </div>
              <div class="detail-item">
                <span class="item-label">手机号</span>
                <div class="item-value-with-copy">
                  <span class="item-value">{{ currentUser.phone }}</span>
                  <el-button
                    type="text"
                    size="small"
                    class="copy-btn"
                    @click="copyToClipboard(currentUser.phone)"
                  >
                    <el-icon><CopyDocument /></el-icon>
                  </el-button>
                </div>
              </div>

            </div>
          </div>

          <div class="detail-group">
            <h4 class="group-title">时间信息</h4>
            <div class="detail-items">
              <div class="detail-item">
                <span class="item-label">注册时间</span>
                <span class="item-value">{{ formatDateTime(currentUser.createTime) }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">最后登录</span>
                <span class="item-value">{{ formatDateTime(currentUser.lastLoginTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Download,
  Search,
  Refresh,
  User,
  CopyDocument
} from '@element-plus/icons-vue'
import { userApi } from '@/api/user'

// 响应式数据
const loading = ref(false)
const userList = ref([])
const detailDialogVisible = ref(false)
const currentUser = ref(null)
const userListRef = ref(null)

// 统计数据
const totalUsers = ref(1234)
const todayNewUsers = ref(56)
const activeUsers = ref(892)

// 查询表单
const queryForm = reactive({
  phone: '',
  nickname: '',
  memberLevel: null,
  status: null,
  registerTimeRange: null
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('复制成功')
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchUserList()
}

// 重置
const handleReset = () => {
  Object.keys(queryForm).forEach(key => {
    if (key === 'status' || key === 'memberLevel' || key === 'registerTimeRange') {
      queryForm[key] = null
    } else {
      queryForm[key] = ''
    }
  })
  pagination.current = 1
  fetchUserList()
  // 滚动到用户列表
  scrollToUserList()
}

// 刷新
const handleRefresh = () => {
  fetchUserList()
  // 滚动到用户列表
  scrollToUserList()
}

// 滚动到用户列表
const scrollToUserList = () => {
  setTimeout(() => {
    if (userListRef.value) {
      userListRef.value.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }, 100)
}



// 查看详情
const handleViewDetail = async (user) => {
  try {
    const response = await userApi.getUserDetail(user.userId)
    if (response.code === 200) {
      currentUser.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  }
}

// 关闭详情对话框
const handleCloseDetail = () => {
  detailDialogVisible.value = false
  currentUser.value = null
}

// 切换用户状态
const handleToggleStatus = async (user) => {
  const action = user.status === 1 ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 ${user.nickname} 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await userApi.updateUserStatus({
      userId: user.userId,
      status: user.status === 1 ? 0 : 1
    })

    if (response.code === 200) {
      user.status = user.status === 1 ? 0 : 1
      user.statusName = user.status === 1 ? '正常' : '禁用'
      ElMessage.success(`${action}成功`)
    } else {
      ElMessage.error(response.message || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}用户失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchUserList()
}

// 当前页改变
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchUserList()
}

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...queryForm
    }

    const response = await userApi.getUserList(params)

    if (response.code === 200) {
      userList.value = response.data.records || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')

    // 如果API调用失败，使用模拟数据
    const mockData = {
      records: [
        {
          userId: 'user001',
          phone: '13800138001',
          nickname: '张三',
          avatar: '',
          memberLevel: 1,
          memberLevelName: '黄金会员',
          status: 1,
          statusName: '正常',
          favoriteCount: 25,
          likeCount: 156,
          commentCount: 89,
          createTime: '2024-01-15T10:30:00',
          lastLoginTime: '2024-01-20T15:45:00'
        },
        {
          userId: 'user002',
          phone: '13800138002',
          nickname: '李四',
          avatar: '',
          memberLevel: 0,
          memberLevelName: '普通会员',
          status: 1,
          statusName: '正常',
          favoriteCount: 12,
          likeCount: 78,
          commentCount: 34,
          createTime: '2024-01-16T09:20:00',
          lastLoginTime: '2024-01-19T14:30:00'
        }
      ],
      total: 2
    }

    userList.value = mockData.records
    pagination.total = mockData.total
  } finally {
    loading.value = false
  }
}

// 添加用户
const handleAddUser = () => {
  ElMessage.info('添加用户功能开发中...')
}

// 导出数据
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

onMounted(() => {
  fetchUserList()
})
</script>

<style lang="scss" scoped>
.users-page {
  .management-panel {
    margin-bottom: 32px;
    background: #FFFFFF;
    border-radius: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #F3F4F6;
    overflow: hidden;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 24px 0 24px;

      .header-left {
        h2 {
          font-size: 28px;
          font-weight: 700;
          color: #1F2937;
          margin: 0;
        }
      }

      .header-right {
        .header-stats {
          display: flex;
          gap: 24px;

          .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 20px;
            background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
            border-radius: 12px;
            border: 1px solid #E2E8F0;
            min-width: 100px;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
              border-color: #8B5CF6;
            }

            .stat-label {
              font-size: 12px;
              color: #64748B;
              margin-bottom: 4px;
              font-weight: 500;
            }

            .stat-value {
              font-size: 24px;
              font-weight: 700;
              color: #8B5CF6;
            }
          }
        }
      }
    }

    .panel-divider {
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, #E5E7EB 20%, #E5E7EB 80%, transparent 100%);
      margin: 24px 0;
    }

    .panel-filters {
      padding: 0 24px 24px 24px;

      .filters-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 24px;

        .search-section {
          flex: 1;

          .search-form {
            .el-form-item {
              margin-bottom: 16px;

              .el-button {
                height: 40px;
                padding: 0 20px;
                border-radius: 8px;
                font-weight: 500;
                font-size: 14px;

                &.el-button--primary {
                  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
                  border: none;

                  &:hover {
                    background: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
                  }
                }

                &:not(.el-button--primary) {
                  background: #F9FAFB;
                  border: 1px solid #E5E7EB;
                  color: #374151;

                  &:hover {
                    background: #F3F4F6;
                    border-color: #D1D5DB;
                    transform: translateY(-1px);
                  }
                }
              }
            }
          }
        }

        .action-section {
          display: flex;
          gap: 12px;
          align-items: flex-start;
          flex-shrink: 0;

          .el-button {
            height: 36px;
            padding: 0 16px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;

            &.el-button--primary {
              background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
              border: none;

              &:hover {
                background: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
              }
            }

            &:not(.el-button--primary) {
              background: #F9FAFB;
              border: 1px solid #E5E7EB;
              color: #374151;

              &:hover {
                background: #F3F4F6;
                border-color: #D1D5DB;
                transform: translateY(-1px);
              }
            }
          }
        }
      }
    }
  }
  
  .content-card {
    background: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #F3F4F6;
    overflow: hidden;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid #F3F4F6;
      background: #FAFBFC;

      .table-title {
        font-size: 18px;
        font-weight: 600;
        color: #1F2937;
      }

      .table-actions {
        .refresh-btn {
          width: 32px;
          height: 32px;
          padding: 0;
          border-radius: 6px;
          background: #F9FAFB;
          border: 1px solid #E5E7EB;
          color: #6B7280;

          &:hover {
            background: #F3F4F6;
            border-color: #D1D5DB;
            color: #374151;
            transform: rotate(180deg);
            transition: all 0.3s ease;
          }

          .el-icon {
            font-size: 16px;
          }
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .user-detail {
    .el-descriptions {
      margin-top: 20px;
    }
  }

  // 用户详情对话框样式
  :deep(.user-detail-dialog) {
    .el-dialog__body {
      padding: 20px;
    }

    .user-detail-content {
      // 用户头像和基本信息
      .user-header {
        display: flex;
        align-items: center;
        gap: 20px;
        padding: 20px;
        background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
        border-radius: 12px;
        margin-bottom: 20px;
        color: white;

        .user-avatar {
          flex-shrink: 0;

          .el-avatar {
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.2);

            .el-icon {
              font-size: 24px;
              color: white;
            }
          }
        }

        .user-info {
          flex: 1;

          .user-name {
            margin: 0 0 6px 0;
            font-size: 18px;
            font-weight: 600;
            color: white;
          }

          .user-phone {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
          }

          .user-tags {
            display: flex;
            gap: 8px;

            .el-tag {
              border: none;
              font-weight: 500;

              // 会员等级标签保持白色背景
              &.el-tag--info,
              &.el-tag--warning,
              &.el-tag--success:not(.status-tag) {
                background: rgba(255, 255, 255, 0.9);
                color: #374151;
              }

              // 状态标签使用对应的颜色
              &.el-tag--success.status-tag {
                background: rgba(34, 197, 94, 0.9);
                color: white;
              }

              &.el-tag--danger.status-tag {
                background: rgba(239, 68, 68, 0.9);
                color: white;
              }
            }
          }
        }

        .user-stats {
          display: flex;
          gap: 24px;

          .stat-item {
            text-align: center;
            min-width: 60px;

            .stat-number {
              font-size: 20px;
              font-weight: 700;
              color: white;
              margin-bottom: 4px;
            }

            .stat-label {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }
      }

      // 详细信息
      .detail-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        align-items: stretch;

        .detail-group {
          background: white;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          overflow: hidden;
          display: flex;
          flex-direction: column;

          .group-title {
            margin: 0;
            padding: 12px 16px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
          }

          .detail-items {
            padding: 16px;
            flex: 1;
            display: flex;
            flex-direction: column;

            .detail-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px 0;
              border-bottom: 1px solid #f3f4f6;
              min-height: 44px;
              flex: 1;

              &:last-child {
                border-bottom: none;
              }

              .item-label {
                font-size: 13px;
                color: #6b7280;
                font-weight: 500;
                flex-shrink: 0;
                width: 80px;
                text-align: left;
              }

              .item-value {
                font-size: 13px;
                color: #111827;
                font-weight: 600;
                flex: 1;
                text-align: right;
              }

              .item-value-with-copy {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 8px;
                flex: 1;
                min-width: 0;

                .tooltip-wrapper {
                  display: inline-block;
                  flex-shrink: 1;
                  min-width: 0;
                }

                .item-value {
                  font-size: 13px;
                  color: #111827;
                  font-weight: 600;

                  &.truncated {
                    max-width: 150px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    cursor: help;
                    display: inline-block;
                  }
                }

                .copy-btn {
                  padding: 4px;
                  min-height: auto;
                  color: #6b7280;
                  flex-shrink: 0;

                  &:hover {
                    color: #667eea;
                    background-color: #f3f4f6;
                  }

                  .el-icon {
                    font-size: 14px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
